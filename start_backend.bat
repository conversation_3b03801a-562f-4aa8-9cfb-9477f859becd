@echo off
echo ========================================
echo Starting Emma Studio Backend Server
echo ========================================
echo.

cd backend

REM Try different ways to run Poetry
echo Attempting to start backend server...

REM Method 1: Try poetry command directly
poetry run uvicorn app.main:app --reload --port 8001
if not errorlevel 1 goto :success

REM Method 2: Try with Python path
echo Trying alternative method...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m poetry run uvicorn app.main:app --reload --port 8001
if not errorlevel 1 goto :success

REM Method 3: Try with pip installed uvicorn
echo Trying with direct Python...
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m pip install uvicorn
C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe -m uvicorn app.main:app --reload --port 8001
if not errorlevel 1 goto :success

echo ERROR: Could not start backend server
echo Please make sure Poetry is installed correctly
pause
exit /b 1

:success
echo Backend server started successfully!
echo Server running at: http://localhost:8001
echo Press Ctrl+C to stop the server
pause
